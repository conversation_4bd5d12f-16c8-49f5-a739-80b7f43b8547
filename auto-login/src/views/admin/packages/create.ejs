<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Create New Package</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/toastify-js@1.12.0/src/toastify.min.css">
  </head>
  <body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-2xl mx-auto">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900">Create New Package</h1>
        <button id="backBtn" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
          Back to Packages
        </button>
      </div>

      <div class="bg-white shadow rounded-lg p-6">
        <div id="errorMessage" class="hidden mb-4 p-4 text-red-700 bg-red-100 rounded-md"></div>
        <div id="successMessage" class="hidden mb-4 p-4 text-green-700 bg-green-100 rounded-md"></div>

        <form id="createPackageForm" class="space-y-4">
          <!-- Package Name -->
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700">Package Name *</label>
            <input
              type="text"
              id="name"
              name="name"
              required
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Premium Package"
            />
          </div>

          <!-- Description -->
          <div>
            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
            <textarea
              id="description"
              name="description"
              rows="3"
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Package description..."
            ></textarea>
          </div>

          <!-- No price or duration_days fields as they've been removed from the Package entity -->

          <!-- Is Best Choice -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="is_best_choice"
              name="is_best_choice"
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label for="is_best_choice" class="ml-2 block text-sm text-gray-700">
              Mark as Best Choice
            </label>
          </div>

          <!-- Has Trail -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="has_trail"
              name="has_trail"
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label for="has_trail" class="ml-2 block text-sm text-gray-700">
              Has Trial
            </label>
          </div>

          <!-- Has Prompt Library -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="has_prompt_library"
              name="has_prompt_library"
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label for="has_prompt_library" class="ml-2 block text-sm text-gray-700">
              Has Prompt Library
            </label>
          </div>

          <!-- Has Prompt Video -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="has_prompt_video"
              name="has_prompt_video"
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label for="has_prompt_video" class="ml-2 block text-sm text-gray-700">
              Has Prompt Video
            </label>
          </div>

          <!-- Is Single Tool -->
          <div class="flex items-center">
            <input
              type="checkbox"
              id="is_single_tool"
              name="is_single_tool"
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label for="is_single_tool" class="ml-2 block text-sm text-gray-700">
              Is Single Tool
            </label>
          </div>

          <!-- Sort Order -->
          <div>
            <label for="sort" class="block text-sm font-medium text-gray-700">Sort Order</label>
            <input
              type="number"
              id="sort"
              name="sort"
              min="0"
              class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="0"
            />
            <p class="mt-1 text-xs text-gray-500">Lower numbers appear first</p>
          </div>

          <!-- Package Features -->
          <div>
            <div class="flex justify-between items-center mb-2">
              <label class="block text-sm font-medium text-gray-700">Features</label>
              <button
                type="button"
                id="addFeatureBtn"
                class="bg-blue-500 hover:bg-blue-700 text-white text-sm py-1 px-2 rounded"
              >
                Add Feature
              </button>
            </div>
            <div id="featuresContainer" class="space-y-2">
              <!-- Feature items will be added here dynamically -->
            </div>
          </div>

          <!-- Package Durations -->
          <div>
            <div class="flex justify-between items-center mb-2">
              <label class="block text-sm font-medium text-gray-700">Additional Durations</label>
              <button
                type="button"
                id="addDurationBtn"
                class="bg-blue-500 hover:bg-blue-700 text-white text-sm py-1 px-2 rounded"
              >
                Add Duration
              </button>
            </div>
            <div id="durationsContainer" class="space-y-3">
              <!-- Duration items will be added here dynamically -->
            </div>
          </div>

          <div class="pt-4">
            <button
              type="submit"
              class="w-full bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
            >
              Create Package
            </button>
          </div>
        </form>
      </div>
    </div>

    <script>
      // Setup axios with credentials
      function setupAxios() {
        // Set axios to send cookies with all requests
        axios.defaults.withCredentials = true;
        return true;
      }

      // Check authentication and admin role
      async function checkAuth() {
        try {
          const response = await axios.get('/auth/status', { withCredentials: true });
          if (!response.data.isAuthenticated) {
            window.location.href = '/login';
            return false;
          }

          // Check if user has admin role
          if (response.data.user && response.data.user.role !== 'admin') {
            // User is authenticated but not an admin
            window.location.href = '/login';
            return false;
          }

          return true;
        } catch (error) {
          window.location.href = '/login';
          return false;
        }
      }

      // Show error message
      function showError(message) {
        const errorElement = document.getElementById('errorMessage');
        errorElement.textContent = message;
        errorElement.classList.remove('hidden');

        // Hide success message if it's visible
        document.getElementById('successMessage').classList.add('hidden');

        // Scroll to error message
        errorElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }

      // Show success message
      function showSuccess(message) {
        const successElement = document.getElementById('successMessage');
        successElement.textContent = message;
        successElement.classList.remove('hidden');

        // Hide error message if it's visible
        document.getElementById('errorMessage').classList.add('hidden');

        // Scroll to success message
        successElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }

      // Function to update discount price display
      function updateDiscountPrice(input) {
        const durationItem = input.closest('.duration-item');
        const priceInput = durationItem.querySelector('.duration-price');
        const discountInput = durationItem.querySelector('.duration-discount');
        const discountPriceDisplay = durationItem.querySelector('.discount-price-display');

        const price = parseFloat(priceInput.value);
        const discountPercent = parseFloat(discountInput.value) || 0;

        if (!isNaN(price) && price > 0) {
          const discountedPrice = (price * (1 - discountPercent / 100)).toFixed(2);
          discountPriceDisplay.textContent = '$' + discountedPrice;

          // Add visual indication if there's a discount
          if (discountPercent > 0) {
            discountPriceDisplay.classList.add('text-green-600', 'font-medium');

            // If the discounted price is the same as the original price (due to rounding),
            // still show it differently to indicate the discount is applied
            if (parseFloat(discountedPrice) === price) {
              discountPriceDisplay.classList.add('text-green-600', 'font-medium');
            }
          } else {
            discountPriceDisplay.classList.remove('text-green-600', 'font-medium');
          }
        } else {
          discountPriceDisplay.textContent = '-';
          discountPriceDisplay.classList.remove('text-green-600', 'font-medium');
        }
      }

      // Function to handle image selection and convert to base64
      function handleImageSelect(event, imgPreview, imgInput) {
        const file = event.target.files[0];
        if (!file) return;

        // Check file size (limit to 1MB)
        if (file.size > 1024 * 1024) {
          showError('Image size should be less than 1MB');
          event.target.value = '';
          return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
          const base64String = e.target.result;
          imgPreview.src = base64String;
          imgPreview.classList.remove('hidden');
          imgInput.value = base64String;
        };
        reader.readAsDataURL(file);
      }

      // Function to add a new feature item
      function addFeatureItem(feature = { description: '', img: '' }) {
        const featuresContainer = document.getElementById('featuresContainer');
        const featureIndex = document.querySelectorAll('.feature-item').length;
        const featureId = `feature-${featureIndex}`;

        const featureItem = document.createElement('div');
        featureItem.className = 'feature-item flex flex-col space-y-2 p-3 border border-gray-300 rounded-md';
        featureItem.innerHTML = `
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <label class="block text-xs text-gray-500">Feature Description</label>
              <input
                type="text"
                class="feature-description w-full px-2 py-1 border border-gray-300 rounded"
                value="${feature.description}"
                placeholder="Feature description"
              />
            </div>
            <div>
              <button
                type="button"
                class="remove-feature-btn bg-red-500 hover:bg-red-700 text-white text-sm py-1 px-2 rounded"
              >
                Remove
              </button>
            </div>
          </div>
          <div>
            <label class="block text-xs text-gray-500">Feature Image</label>
            <div class="flex items-center space-x-2">
              <input
                type="file"
                accept="image/*"
                class="feature-image-file w-full px-2 py-1 border border-gray-300 rounded"
                id="${featureId}-file"
              />
              <input
                type="hidden"
                class="feature-image-data"
                value="${feature.img || ''}"
              />
            </div>
            <div class="mt-2">
              <img
                src="${feature.img || ''}"
                class="feature-image-preview ${feature.img ? '' : 'hidden'} h-20 object-contain border rounded"
              />
            </div>
          </div>
        `;

        featuresContainer.appendChild(featureItem);

        // Add event listener to the remove button
        featureItem.querySelector('.remove-feature-btn').addEventListener('click', () => {
          featureItem.remove();
        });

        // Add event listener to the file input
        const fileInput = featureItem.querySelector('.feature-image-file');
        const imgPreview = featureItem.querySelector('.feature-image-preview');
        const imgInput = featureItem.querySelector('.feature-image-data');

        fileInput.addEventListener('change', (e) => {
          handleImageSelect(e, imgPreview, imgInput);
        });
      }

      // Function to add a new duration item
      function addDurationItem(durationDays = '', price = '', discountPercent = '') {
        const durationsContainer = document.getElementById('durationsContainer');
        const durationIndex = document.querySelectorAll('.duration-item').length;

        const durationItem = document.createElement('div');
        durationItem.className = 'duration-item flex items-center space-x-2 p-3 border border-gray-300 rounded-md';
        durationItem.innerHTML = `
          <div class="flex-1">
            <label class="block text-xs text-gray-500">Duration (Days)</label>
            <input
              type="number"
              class="duration-days w-full px-2 py-1 border border-gray-300 rounded"
              min="1"
              value="${durationDays}"
              placeholder="30"
              required
            />
          </div>
          <div class="flex-1">
            <label class="block text-xs text-gray-500">Price ($)</label>
            <input
              type="number"
              class="duration-price w-full px-2 py-1 border border-gray-300 rounded"
              min="0"
              step="0.01"
              value="${price}"
              placeholder="9.99"
              required
              oninput="updateDiscountPrice(this)"
            />
          </div>
          <div class="flex-1">
            <label class="block text-xs text-gray-500">Discount (%)</label>
            <input
              type="number"
              class="duration-discount w-full px-2 py-1 border border-gray-300 rounded"
              min="0"
              max="100"
              step="0.01"
              value="${discountPercent}"
              placeholder="0"
              oninput="updateDiscountPrice(this)"
            />
          </div>
          <div class="flex-1">
            <label class="block text-xs text-gray-500">Discount Price ($)</label>
            <div class="discount-price-display px-2 py-1 bg-gray-100 rounded">
              -
            </div>
          </div>
          <div class="flex items-end pb-1">
            <button
              type="button"
              class="remove-duration-btn bg-red-500 hover:bg-red-700 text-white text-sm py-1 px-2 rounded"
            >
              Remove
            </button>
          </div>
        `;

        durationsContainer.appendChild(durationItem);

        // Add event listener to the remove button
        durationItem.querySelector('.remove-duration-btn').addEventListener('click', () => {
          durationItem.remove();
        });

        // Always initialize discount price display
        setTimeout(() => {
          updateDiscountPrice(durationItem.querySelector('.duration-price'));
        }, 0);
      }

      // Create package
      async function createPackage(formData) {
        if (!setupAxios()) return;

        try {
          console.log('Sending data to server:', formData);

          const response = await axios.post('/packages', formData);
          showSuccess('Package created successfully!');
          document.getElementById('createPackageForm').reset();

          // Redirect after a short delay
          setTimeout(() => {
            window.location.href = '/admin/packages';
          }, 2000);

          return response.data;
        } catch (error) {
          console.error('Error creating package:', error);
          if (error.response && error.response.status === 401) {
            window.location.href = '/login';
          } else {
            const message = error.response?.data?.message || 'Failed to create package. Please try again.';
            showError(message);
          }
          return null;
        }
      }

      // Document ready
      document.addEventListener('DOMContentLoaded', async () => {
        setupAxios();
        if (!(await checkAuth())) return;

        // Back button
        document.getElementById('backBtn').addEventListener('click', () => {
          window.location.href = '/admin/packages';
        });

        // Add feature button
        document.getElementById('addFeatureBtn').addEventListener('click', () => {
          addFeatureItem();
        });

        // Add duration button
        document.getElementById('addDurationBtn').addEventListener('click', () => {
          addDurationItem();
        });

        // Form submission
        document.getElementById('createPackageForm').addEventListener('submit', async (e) => {
          e.preventDefault();

          // Get all duration items
          const durationItems = document.querySelectorAll('.duration-item');
          const durations = [];

          // Extract data from each duration item
          durationItems.forEach(item => {
            const durationDays = parseInt(item.querySelector('.duration-days').value, 10);
            const price = parseFloat(item.querySelector('.duration-price').value);
            const discountPercent = parseFloat(item.querySelector('.duration-discount').value) || 0;

            if (!isNaN(durationDays) && !isNaN(price)) {
              durations.push({
                duration_days: durationDays,
                price: price,
                discount_percent: discountPercent
              });
            }
          });

          // Get all feature items
          const featureItems = document.querySelectorAll('.feature-item');
          const features = [];

          // Extract data from each feature item
          featureItems.forEach(item => {
            const description = item.querySelector('.feature-description').value.trim();
            const img = item.querySelector('.feature-image-data').value;

            if (description) {
              features.push({
                description,
                img: img || ''
              });
            }
          });

          // Create a direct JavaScript object
          const formData = {
            name: document.getElementById('name').value,
            description: document.getElementById('description').value || '',
            is_best_choice: document.getElementById('is_best_choice').checked,
            has_trail: document.getElementById('has_trail').checked,
            has_prompt_library: document.getElementById('has_prompt_library').checked,
            has_prompt_video: document.getElementById('has_prompt_video').checked,
            is_single_tool: document.getElementById('is_single_tool').checked,
            sort: parseInt(document.getElementById('sort').value) || 0,
            features: features,
            durations: durations
          };

          await createPackage(formData);
        });
      });
    </script>
  </body>
</html>
