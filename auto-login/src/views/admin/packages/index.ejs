<%- include('../../partials/layout', {
  title: 'Package Management',
  currentPath: '/admin/packages',
  body: `
    <div class="container mx-auto">
      <div class="flex justify-between items-center mb-6">
        <div>
          <button id="createPackageBtn" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
            Create New Package
          </button>
          <button id="backToHomeBtn" class="ml-2 bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
            Back to Home
          </button>
        </div>
      </div>

      <!-- Search and Filter -->
      <div class="bg-white p-4 rounded-lg shadow mb-6">
        <div class="flex flex-col md:flex-row gap-4">
          <div class="flex-1">
            <label for="searchInput" class="block text-sm font-medium text-gray-700 mb-1">Search</label>
            <input
              type="text"
              id="searchInput"
              placeholder="Search by name or description"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            />
          </div>
          <div class="w-full md:w-48">
            <label for="filterSelect" class="block text-sm font-medium text-gray-700 mb-1">Filter by Price</label>
            <select
              id="filterSelect"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            >
              <option value="all">All Packages</option>
              <option value="low_price">Low Price (< $50)</option>
              <option value="medium_price">Medium Price ($50 - $100)</option>
              <option value="high_price">High Price (> $100)</option>
            </select>
          </div>
          <div class="flex items-end">
            <button
              id="applyFilterBtn"
              class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
            >
              Apply Filters
            </button>
          </div>
        </div>
      </div>

      <!-- Packages Table -->
      <div class="bg-white shadow rounded-lg overflow-hidden">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">ID</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Best Choice</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Has Trial</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prompt Library</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prompt Video</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Single Tool</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sort</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody id="packagesTableBody" class="bg-white divide-y divide-gray-200">
            <!-- Package rows will be inserted here -->
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="mt-4 flex justify-between items-center">
        <div>
          <button id="prevPageBtn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-l">
            Previous
          </button>
          <button id="nextPageBtn" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-2 px-4 rounded-r">
            Next
          </button>
        </div>
        <div class="text-gray-600">
          Page <span id="currentPage">1</span> of <span id="totalPages">1</span>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteModal" class="modal opacity-0 pointer-events-none fixed w-full h-full top-0 left-0 flex items-center justify-center z-50 hidden">
      <div class="modal-overlay absolute w-full h-full bg-gray-900 opacity-50"></div>

      <div class="modal-container bg-white w-11/12 md:max-w-md mx-auto rounded shadow-lg z-50 overflow-y-auto">
        <div class="modal-content py-4 text-left px-6">
          <div class="flex justify-between items-center pb-3">
            <p class="text-2xl font-bold">Confirm Delete</p>
            <div class="modal-close cursor-pointer z-50" onclick="hideDeleteModal()">
              <svg class="fill-current text-black" xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18">
                <path d="M14.53 4.53l-1.06-1.06L9 7.94 4.53 3.47 3.47 4.53 7.94 9l-4.47 4.47 1.06 1.06L9 10.06l4.47 4.47 1.06-1.06L10.06 9z"></path>
              </svg>
            </div>
          </div>

          <p>Are you sure you want to delete this package? This action cannot be undone.</p>

          <div class="flex justify-end pt-2">
            <button class="modal-close px-4 bg-gray-200 p-3 rounded-lg text-black hover:bg-gray-300 mr-2" onclick="hideDeleteModal()">Cancel</button>
            <button class="px-4 bg-red-500 p-3 rounded-lg text-white hover:bg-red-600" onclick="deletePackage()">Delete</button>
          </div>
        </div>
      </div>
    </div>
  `,
  additionalScripts: `
    <script>
      // Global variables
      let currentPage = 1;
      let searchTerm = '';
      let filterValue = 'all';
      let deletePackageId = null;

      // Load packages with pagination, search and filter
      async function loadPackages(page = 1) {
        try {
          let url = '/packages?page=' + page + '&limit=10';

          // Add search and filter parameters if they exist
          if (searchTerm) {
            url += '&search=' + encodeURIComponent(searchTerm);
          }

          if (filterValue !== 'all') {
            url += '&filter=' + filterValue;
          }

          const response = await axios.get(url);
          const packages = response.data.data;
          const totalPages = response.data.meta.totalPages;

          document.getElementById('currentPage').textContent = page;
          document.getElementById('totalPages').textContent = totalPages;

          renderPackagesTable(packages);
        } catch (error) {
          handleApiError(error);
        }
      }

      // Render packages table
      function renderPackagesTable(packages) {
        const tableBody = document.getElementById('packagesTableBody');
        tableBody.innerHTML = '';

        if (packages.length === 0) {
          const emptyRow = document.createElement('tr');
          emptyRow.innerHTML =
            '<td colspan="10" class="px-6 py-4 text-center text-gray-500">' +
              'No packages found' +
            '</td>';
          tableBody.appendChild(emptyRow);
          return;
        }

        packages.forEach(package_ => {
          const row = document.createElement('tr');
          row.className = 'hover:bg-gray-50';

          row.innerHTML =
            '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">' + package_.id + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + package_.name + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + (package_.description || '-') + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + (package_.is_best_choice ? 'Yes' : 'No') + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + (package_.has_trail ? 'Yes' : 'No') + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + (package_.has_prompt_library ? 'Yes' : 'No') + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + (package_.has_prompt_video ? 'Yes' : 'No') + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + (package_.is_single_tool ? 'Yes' : 'No') + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">' + (package_.sort || '0') + '</td>' +
            '<td class="px-6 py-4 whitespace-nowrap text-sm font-medium">' +
              '<div class="flex space-x-2">' +
                '<button onclick="viewPackage(' + package_.id + ')" class="text-indigo-600 hover:text-indigo-900">View</button>' +
                '<button onclick="editPackage(' + package_.id + ')" class="text-blue-600 hover:text-blue-900">Edit</button>' +
                '<button onclick="showDeleteModal(' + package_.id + ')" class="text-red-600 hover:text-red-900">Delete</button>' +
              '</div>' +
            '</td>';

          tableBody.appendChild(row);
        });
      }

      // View package details
      function viewPackage(id) {
        window.location.href = '/admin/packages/' + id;
      }

      // Edit package
      function editPackage(id) {
        window.location.href = '/admin/packages/' + id + '/edit';
      }

      // Show delete confirmation modal
      function showDeleteModal(id) {
        deletePackageId = id;
        document.getElementById('deleteModal').classList.remove('hidden');
        document.getElementById('deleteModal').classList.remove('opacity-0');
        document.getElementById('deleteModal').classList.add('opacity-100');
        document.getElementById('deleteModal').classList.add('pointer-events-auto');
        document.body.classList.add('modal-active');
      }

      // Hide delete confirmation modal
      function hideDeleteModal() {
        document.getElementById('deleteModal').classList.add('opacity-0');
        document.getElementById('deleteModal').classList.remove('opacity-100');
        document.getElementById('deleteModal').classList.add('pointer-events-none');
        setTimeout(() => {
          document.getElementById('deleteModal').classList.add('hidden');
          document.body.classList.remove('modal-active');
        }, 300);
        deletePackageId = null;
      }

      // Delete package
      async function deletePackage() {
        if (!deletePackageId) return;

        try {
          await axios.delete('/packages/' + deletePackageId);
          hideDeleteModal();
          showToast('Package deleted successfully');
          loadPackages(currentPage);
        } catch (error) {
          hideDeleteModal();
          handleApiError(error);
        }
      }

      // Document ready
      document.addEventListener('DOMContentLoaded', async () => {
        // Initial load
        loadPackages(currentPage);

        // Pagination
        document.getElementById('prevPageBtn').addEventListener('click', () => {
          if (currentPage > 1) {
            currentPage--;
            loadPackages(currentPage);
          }
        });

        document.getElementById('nextPageBtn').addEventListener('click', () => {
          const totalPages = parseInt(document.getElementById('totalPages').textContent);
          if (currentPage < totalPages) {
            currentPage++;
            loadPackages(currentPage);
          }
        });

        // Search and filter
        document.getElementById('applyFilterBtn').addEventListener('click', () => {
          searchTerm = document.getElementById('searchInput').value.trim();
          filterValue = document.getElementById('filterSelect').value;
          currentPage = 1;
          loadPackages(currentPage);
        });

        // Create new package
        document.getElementById('createPackageBtn').addEventListener('click', () => {
          window.location.href = '/admin/packages/create';
        });

        // Back to home
        document.getElementById('backToHomeBtn').addEventListener('click', () => {
          window.location.href = '/';
        });
      });
    </script>
  `
}) %>
