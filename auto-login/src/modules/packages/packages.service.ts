import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Package } from './entities/package.entity';
import { PackageUser } from './entities/package-user.entity';
import { PackageDuration } from './entities/package-duration.entity';
import { CreatePackageDto } from './dto/create-package.dto';
import { UpdatePackageDto } from './dto/update-package.dto';
import {
  calculateDiscountedPrice,
  convertCurrency,
} from '../subscriptions/utils/pricing.utils';
import { Account } from '../accounts/entities/account.entity';

@Injectable()
export class PackagesService {
  constructor(
    @InjectRepository(Package)
    private packageRepository: Repository<Package>,
    @InjectRepository(PackageUser)
    private packageUserRepository: Repository<PackageUser>,
    @InjectRepository(PackageDuration)
    private packageDurationRepository: Repository<PackageDuration>,
  ) {}

  async create(createPackageDto: CreatePackageDto): Promise<Package> {
    // Extract durations from the DTO
    const { durations, ...packageData } = createPackageDto;

    // Create and save the package
    const newPackage = this.packageRepository.create(packageData);
    const savedPackage = await this.packageRepository.save(newPackage);

    // Create and save package durations if provided
    if (durations && durations.length > 0) {
      const durationEntities = durations.map((duration) =>
        this.packageDurationRepository.create({
          package_id: savedPackage.id,
          duration_days: duration.duration_days,
          price: duration.price,
          discount_percent: duration.discount_percent || 0,
        }),
      );

      await this.packageDurationRepository.save(durationEntities);
    } else {
      // If no durations provided, create a default duration
      const defaultDuration = this.packageDurationRepository.create({
        package_id: savedPackage.id,
        duration_days: 30, // Default to 30 days
        price: 9.99, // Default price
        discount_percent: 0, // No discount by default
      });

      await this.packageDurationRepository.save(defaultDuration);
    }

    return savedPackage;
  }

  async findAll(
    page: number = 1,
    limit: number = 10,
    search?: string,
    filter?: string,
  ) {
    const skip = (page - 1) * limit;

    // Build the base query
    let query = `SELECT * FROM packages`;
    const queryParams: any[] = [];
    let whereClause = '';

    // Apply search filter if provided
    if (search) {
      whereClause = ` WHERE (name ILIKE $1 OR description ILIKE $1)`;
      queryParams.push(`%${search}%`);
    }

    // Apply price filter if provided
    if (filter) {
      const priceCondition = this.getPriceFilterCondition(filter);
      if (priceCondition) {
        whereClause = whereClause
          ? `${whereClause} AND ${priceCondition}`
          : ` WHERE ${priceCondition}`;
      }
    }

    // Add where clause to query
    query += whereClause;

    // Add order by, limit and offset
    // Sort by sort column first (ascending), then by created_at (descending)
    query += ` ORDER BY sort ASC, created_at DESC LIMIT $${queryParams.length + 1} OFFSET $${queryParams.length + 2}`;
    queryParams.push(limit, skip);

    // Execute the query
    const packages = await this.packageRepository.manager.query(
      query,
      queryParams,
    );

    // Get total count for pagination
    const countQuery = `SELECT COUNT(*) as total FROM packages${whereClause}`;
    const totalResult = await this.packageRepository.manager.query(
      countQuery,
      queryParams.slice(0, -2),
    );
    const total = parseInt(totalResult[0].total, 10);

    // Get package users and accounts for each package
    const packageIds = packages.map((p: any) => p.id);

    // Only proceed if we have packages
    if (packageIds.length > 0) {
      // Get all package users with their users in one query
      const packageUsersQuery = `
        SELECT pu.*, u.*, pu.package_id
        FROM package_users pu
        JOIN users u ON pu.user_id = u.id
        WHERE pu.package_id = ANY($1)
      `;

      const packageUsersData = await this.packageRepository.manager.query(
        packageUsersQuery,
        [packageIds],
      );

      // Get all accounts with their junction data in one query
      const accountsQuery = `
        SELECT a.*, ap.package_id
        FROM accounts a
        JOIN account_packages ap ON a.id = ap.account_id
        WHERE ap.package_id = ANY($1)
      `;

      const accountsData = await this.packageRepository.manager.query(
        accountsQuery,
        [packageIds],
      );

      // Map package users and accounts to their respective packages
      for (const package_ of packages) {
        // Map package users
        package_.packageUsers = packageUsersData
          .filter((pu: any) => pu.package_id === package_.id)
          .map((row: any) => ({
            id: row.id,
            user_id: row.user_id,
            package_id: row.package_id,
            start_date: row.start_date,
            expires_at: row.expires_at,
            status: row.status,
            created_at: row.created_at,
            updated_at: row.updated_at,
            user: {
              status: row.status,
              role: row.role,
              created_at: row.created_at,
              updated_at: row.updated_at,
              // Exclude password, plain_password, discord_id, google_id, expired_date
            },
          }));

        // Map accounts
        package_.accounts = accountsData
          .filter((a: any) => a.package_id === package_.id)
          .map((row: any) => ({
            id: row.id,
            name: row.name,
            website_url: row.website_url,
            img_intro: row.img_intro,
            login_cookie: row.login_cookie,
            created_at: row.created_at,
            updated_at: row.updated_at,
            account_packages: [
              {
                account_id: row.id,
                package_id: package_.id,
              },
            ],
          }));
      }
    }

    return {
      data: packages,
      meta: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  // Helper method to get price filter condition
  private getPriceFilterCondition(filter: string): string | null {
    // Since price is now in package_durations, we need to use a subquery
    switch (filter) {
      case 'low_price':
        return 'id IN (SELECT package_id FROM package_durations WHERE price < 50)';
      case 'medium_price':
        return 'id IN (SELECT package_id FROM package_durations WHERE price >= 50 AND price <= 100)';
      case 'high_price':
        return 'id IN (SELECT package_id FROM package_durations WHERE price > 100)';
      default:
        return null;
    }
  }

  async findOne(id: number): Promise<any> {
    // Get the package basic data
    const packageData = await this.packageRepository.manager.query(
      `SELECT * FROM packages WHERE id = $1`,
      [id],
    );

    if (!packageData || packageData.length === 0) {
      throw new NotFoundException(`Package with ID ${id} not found`);
    }

    const package_ = packageData[0];

    // Get package durations
    const packageDurations = await this.packageRepository.manager.query(
      `SELECT * FROM package_durations WHERE package_id = $1 ORDER BY duration_days ASC`,
      [id],
    );

    // Get package users with user data
    const packageUsersData = await this.packageRepository.manager.query(
      `SELECT pu.*, u.*
       FROM package_users pu
       JOIN users u ON pu.user_id = u.id
       WHERE pu.package_id = $1`,
      [id],
    );

    // Format package users data
    const packageUsers = packageUsersData.map((row: any) => {
      return {
        id: row.id,
        user_id: row.user_id,
        package_id: row.package_id,
        start_date: row.start_date,
        expires_at: row.expires_at,
        status: row.status,
        created_at: row.created_at,
        updated_at: row.updated_at,
        user: {
          status: row.status,
          role: row.role,
          created_at: row.created_at,
          updated_at: row.updated_at,
          // Exclude password, plain_password, discord_id, google_id, expired_date
        },
      };
    });

    // Get accounts data
    const accountsQuery = `
      SELECT a.*
      FROM accounts a
      JOIN account_packages ap ON a.id = ap.account_id
      WHERE ap.package_id = $1
    `;

    const accountsData = await this.packageRepository.manager.query(
      accountsQuery,
      [id],
    );

    // Format accounts data
    const accounts = accountsData.map((row: any) => {
      const account = {
        id: row.id,
        name: row.name,
        website_url: row.website_url,
        img_intro: row.img_intro,
        login_cookie: row.login_cookie,
        created_at: row.created_at,
        updated_at: row.updated_at,
        account_packages: [
          {
            account_id: row.id,
            package_id: id,
          },
        ],
      };
      return account;
    });

    // Combine all data
    return {
      ...package_,
      durations: packageDurations,
      packageUsers,
      accounts,
    };
  }

  async update(
    id: number,
    updatePackageDto: UpdatePackageDto,
  ): Promise<Package> {
    // Extract durations from the DTO
    const { durations, ...packageData } = updatePackageDto as any;

    // Get the package
    const package_ = await this.packageRepository.findOne({ where: { id } });

    if (!package_) {
      throw new NotFoundException(`Package with ID ${id} not found`);
    }

    // Update the package basic data
    this.packageRepository.merge(package_, packageData);
    const updatedPackage = await this.packageRepository.save(package_);

    // Handle durations if provided
    if (durations && Array.isArray(durations)) {
      // Get existing durations
      const existingDurations = await this.packageDurationRepository.find({
        where: { package_id: id },
      });

      // Create a map of existing durations by ID for easy lookup
      const existingDurationsMap = new Map();
      existingDurations.forEach((duration) => {
        existingDurationsMap.set(duration.id, duration);
      });

      // Process each duration in the update DTO
      const durationsToSave = [];
      const processedDurationIds = new Set();

      for (const duration of durations) {
        if (duration.id && existingDurationsMap.has(duration.id)) {
          // Update existing duration
          const existingDuration = existingDurationsMap.get(duration.id);
          existingDuration.duration_days = duration.duration_days;
          existingDuration.price = duration.price;
          existingDuration.discount_percent =
            duration.discount_percent || existingDuration.discount_percent || 0;
          durationsToSave.push(existingDuration);
          processedDurationIds.add(duration.id);
        } else {
          // Create new duration
          const newDuration = this.packageDurationRepository.create({
            package_id: id,
            duration_days: duration.duration_days,
            price: duration.price,
            discount_percent: duration.discount_percent || 0,
          });
          durationsToSave.push(newDuration);
        }
      }

      // Save all durations
      await this.packageDurationRepository.save(durationsToSave);

      // Delete durations that were not included in the update
      const durationsToDelete = existingDurations.filter(
        (duration) => !processedDurationIds.has(duration.id),
      );

      if (durationsToDelete.length > 0) {
        await this.packageDurationRepository.remove(durationsToDelete);
      }
    }

    return updatedPackage;
  }

  async remove(id: number): Promise<void> {
    // First, find the package entity directly from the repository
    const package_ = await this.packageRepository.findOne({ where: { id } });

    if (!package_) {
      throw new NotFoundException(`Package with ID ${id} not found`);
    }

    // Delete related package durations
    await this.packageDurationRepository.delete({ package_id: id });

    // Delete related package users
    await this.packageUserRepository.delete({ package_id: id });

    // Delete relationships in the account_packages junction table
    await this.packageRepository.manager.query(
      `DELETE FROM account_packages WHERE package_id = $1`,
      [id],
    );

    // Finally, delete the package itself
    await this.packageRepository.delete(id);
  }

  async findPackageDuration(id: number): Promise<any> {
    const duration = await this.packageDurationRepository.findOne({
      where: { id },
    });

    if (!duration) {
      throw new NotFoundException(`Package duration with ID ${id} not found`);
    }

    return duration;
  }

  async assignToUser(
    packageId: number,
    userId: number,
    expiresAt: Date,
    durationId?: number,
  ): Promise<void> {
    // Check if the package exists
    const package_ = await this.packageRepository.findOne({
      where: { id: packageId },
    });

    if (!package_) {
      throw new NotFoundException(`Package with ID ${packageId} not found`);
    }

    // If durationId is provided, verify it exists and belongs to this package
    if (durationId) {
      const duration = await this.packageDurationRepository.findOne({
        where: { id: durationId, package_id: packageId },
      });

      if (!duration) {
        throw new NotFoundException(
          `Duration with ID ${durationId} not found for package ${packageId}`,
        );
      }
    }

    // Check if the user already has this package
    const existingPackageUser = await this.packageUserRepository.findOne({
      where: { user_id: userId, package_id: packageId },
    });

    if (existingPackageUser) {
      // Update existing package user
      existingPackageUser.expires_at = expiresAt;
      existingPackageUser.status =
        new Date() > expiresAt ? 'expired' : 'active';
      await this.packageUserRepository.save(existingPackageUser);
    } else {
      // Create new package user
      const newPackageUser = this.packageUserRepository.create({
        user_id: userId,
        package_id: packageId,
        expires_at: expiresAt,
        status: new Date() > expiresAt ? 'expired' : 'active',
      });
      await this.packageUserRepository.save(newPackageUser);
    }
  }

  async removeFromUser(packageId: number, userId: number): Promise<void> {
    const packageUser = await this.packageUserRepository.findOne({
      where: { user_id: userId, package_id: packageId },
    });

    if (packageUser) {
      await this.packageUserRepository.remove(packageUser);
    }
  }

  async assignToAccount(packageId: number, accountId: number): Promise<void> {
    // Check if the package exists
    const package_ = await this.packageRepository.findOne({
      where: { id: packageId },
    });

    if (!package_) {
      throw new NotFoundException(`Package with ID ${packageId} not found`);
    }

    // Get the account repository through the entity manager
    const accountRepository =
      this.packageRepository.manager.getRepository(Account);

    // Find the account
    const account = await accountRepository.findOne({
      where: { id: accountId },
    });

    if (!account) {
      throw new NotFoundException(`Account with ID ${accountId} not found`);
    }

    // Use a direct query to insert into the junction table
    try {
      // Check if the relationship already exists
      const queryRunner =
        this.packageRepository.manager.connection.createQueryRunner();
      await queryRunner.connect();

      // Start transaction
      await queryRunner.startTransaction();

      try {
        // Check if the relationship already exists
        const existingRelation = await queryRunner.manager.query(
          `SELECT * FROM account_packages WHERE account_id = $1 AND package_id = $2`,
          [accountId, packageId],
        );

        if (existingRelation.length === 0) {
          // Insert the relationship
          await queryRunner.manager.query(
            `INSERT INTO account_packages (account_id, package_id) VALUES ($1, $2)`,
            [accountId, packageId],
          );
        }

        // Commit transaction
        await queryRunner.commitTransaction();
      } catch (err) {
        // Rollback transaction on error
        await queryRunner.rollbackTransaction();
        throw err;
      } finally {
        // Release the query runner
        await queryRunner.release();
      }
    } catch (error) {
      console.error('Error assigning package to account:', error);
      throw new Error(`Failed to assign package to account: ${error.message}`);
    }
  }

  async removeFromAccount(packageId: number, accountId: number): Promise<void> {
    // Check if the package exists
    const package_ = await this.packageRepository.findOne({
      where: { id: packageId },
    });

    if (!package_) {
      return; // Nothing to remove
    }

    // Use a direct query to delete from the junction table
    try {
      await this.packageRepository.manager.query(
        `DELETE FROM account_packages WHERE account_id = $1 AND package_id = $2`,
        [accountId, packageId],
      );
    } catch (error) {
      console.error('Error removing package from account:', error);
      throw new Error(
        `Failed to remove package from account: ${error.message}`,
      );
    }
  }

  async findPackageDurations(
    days?: number[],
    currency: string = 'USD',
  ): Promise<any> {
    try {
      // Build the base query to get package durations with their package info
      let query = `
        SELECT pd.*, p.name as package_name, p.description as package_description, p.features as package_features,
        p.is_best_choice, p.has_trail, p.has_prompt_library, p.has_prompt_video, p.sort, p.is_single_tool
        FROM package_durations pd
        JOIN packages p ON pd.package_id = p.id
      `;

      const queryParams: any[] = [];

      // Add filter for specific durations if provided
      if (days && days.length > 0) {
        query += ` WHERE pd.duration_days IN (`;
        days.forEach((day, index) => {
          query += index === 0 ? `$${index + 1}` : `, $${index + 1}`;
          queryParams.push(day);
        });
        query += `)`;
      }

      // Add order by clause - sort by package sort field first, then by package_id and duration_days
      query += ` ORDER BY p.sort ASC, pd.package_id, pd.duration_days`;

      // Execute the query
      const durations = await this.packageDurationRepository.manager.query(
        query,
        queryParams,
      );

      // Group durations by package
      const packageMap = new Map();

      for (const duration of durations) {
        const packageId = duration.package_id;

        if (!packageMap.has(packageId)) {
          packageMap.set(packageId, {
            id: packageId,
            name: duration.package_name,
            description: duration.package_description,
            features: duration.package_features || [],
            is_best_choice: duration.is_best_choice || false,
            is_single_tool: duration.is_single_tool || false,
            has_trail: duration.has_trail || false,
            has_prompt_library: duration.has_prompt_library || false,
            has_prompt_video: duration.has_prompt_video || false,
            sort: duration.sort || 0,
            durations: [],
          });
        }

        // Calculate price based on currency
        const price = parseFloat(duration.price);
        const convertedPrice = convertCurrency(price, currency);

        // Calculate discounted price using utility function
        const discountPercent = parseFloat(duration.discount_percent) || 0;
        const originalPrice = convertedPrice;
        const discountedPrice = calculateDiscountedPrice(
          convertedPrice,
          discountPercent,
        );

        packageMap.get(packageId).durations.push({
          id: duration.id,
          package_id: packageId,
          duration_days: duration.duration_days,
          price: discountedPrice.toFixed(2),
          original_price: originalPrice.toFixed(2),
          discount_percent: discountPercent,
          currency: currency,
        });
      }

      // Convert map to array
      return Array.from(packageMap.values());
    } catch (error) {
      console.error('Error finding package durations:', error);
      throw new Error(`Failed to find package durations: ${error.message}`);
    }
  }
}
